/* eslint-disable @typescript-eslint/no-explicit-any */
import { useParams, useNavigate } from 'react-router-dom';
import { PageTitle } from '../../../../components/helmet/helmet';
import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Tag2,
  Gift,
  Moneys,
  // ArrowLeft,
  // EyeSlash,
  Box,
  BuyCrypto,
  AddCircle,
  Edit,
  EyeSlash,
  Edit2,
} from 'iconsax-react';
import gift from '../../../../assets/images/gift-res.png';
import empty from '../../../../assets/images/empy.png';
import { Icon } from '../../../../components/icons/icon';
import iphone from '../../../../assets/images/iphone.svg';
import { CreateGiftRegistry } from '../create-gift-registry';
import { useInfiniteQuery } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';
import { useEventStore } from '../../../../lib/store/event';
import { EyeOff, MoreVertical } from 'lucide-react';

export const GiftRegistryDetails = () => {
  const { selectedEvent } = useEventStore();
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('gifts');
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  const {
    data: giftItemData,
    isLoading: isGiftLoading,
    error: giftError,
    fetchNextPage: fetchNextGiftPage,
    hasNextPage: hasNextGiftPage,
    isFetchingNextPage: isFetchingNextGiftPage,
  } = useInfiniteQuery({
    queryKey: ['gift-items', selectedEvent?.id],
    queryFn: async ({ pageParam = 1 }) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return GiftRegistryServices.getGiftItems(selectedEvent.id, {
        page: pageParam,
        per_page: 5,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data?.meta) return undefined;
      const meta = lastPage.data.meta;
      return meta.next_page ? meta.page + 1 : undefined;
    },
    enabled: !!selectedEvent?.id,
  });

  const {
    data: cashGiftData,
    isLoading: isCashLoading,
    error: cashError,
    fetchNextPage: fetchNextCashPage,
    hasNextPage: hasNextCashPage,
    isFetchingNextPage: isFetchingNextCashPage,
  } = useInfiniteQuery({
    queryKey: ['cash-gifts', selectedEvent?.id],
    queryFn: async ({ pageParam = 1 }) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return GiftRegistryServices.getCashGifts(selectedEvent.id, {
        page: pageParam,
        per_page: 5,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data?.meta) return undefined;
      const meta = lastPage.data.meta;
      return meta.next_page ? meta.page + 1 : undefined;
    },
    enabled: !!selectedEvent?.id,
  });

  const handleTabScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      if (scrollHeight - scrollTop <= clientHeight + 5) {
        if (
          activeTab === 'gifts' &&
          hasNextGiftPage &&
          !isFetchingNextGiftPage
        ) {
          fetchNextGiftPage();
        } else if (
          activeTab === 'cash' &&
          hasNextCashPage &&
          !isFetchingNextCashPage
        ) {
          fetchNextCashPage();
        }
      }
    },
    [
      activeTab,
      hasNextGiftPage,
      hasNextCashPage,
      isFetchingNextGiftPage,
      isFetchingNextCashPage,
      fetchNextGiftPage,
      fetchNextCashPage,
    ]
  );

  const allGiftItems =
    giftItemData?.pages?.flatMap((page) => page?.data?.gifts || []) || [];

  const allCashItems =
    cashGiftData?.pages?.flatMap((page) => page?.data?.gifts || []) || [];

  const registry = {
    reservedItems: 0,
    receivedCash: 0,
    amountReceived: 0,
    items: allGiftItems.map((item: any) => ({
      id: item.id,
      name: item.name,
      price: parseFloat(item.price) || 0,
      image: item.image_preview_url || iphone,
      description: item.description || '',
      reserved: item.reserved_count || 0,
      purchased: item.purchased_count || 0,
    })),
    cashItems: allCashItems.map((item: any) => ({
      id: item.id,
      amount: parseFloat(item.amount) || 0,
      description: item.description,
      received: item.received_count || 0,
    })),
  };
  const handleDropdownToggle = (e) => {
    e.stopPropagation(); // Prevent card click
    setIsDropdownOpen(!isDropdownOpen);
  };
  const handleHideFromList = (e) => {
    e.stopPropagation();
    setIsDropdownOpen(false);
    // Add your hide logic here
    // console.log('Hide from list:', item.id);
  };

  const handleEditGiftItem = (e) => {
    e.stopPropagation();
    setIsDropdownOpen(false);
    // Add your edit logic here
    // console.log('Edit gift item:', item.id);
  };
  const guestStats = [
    {
      label: 'GIFT ITEMS',
      count: registry?.items?.length || 0,
      icon: <Icon name="giftItems" />,
    },
    {
      label: 'RESERVED ITEMS',
      count: registry?.reservedItems || 0,
      icon: <Icon name="reserved" />,
    },
    {
      label: 'CASH GIFTS',
      count: registry?.cashItems?.length || 0,
      icon: <Icon name="cashGift" color="#F5F4FF" secondaryColor="#7F7AB2" />,
    },
    {
      label: 'RECEIVED CASH',
      count: registry?.receivedCash || 0,
      icon: <Icon name="markedCircle" />,
    },
  ];

  if (isGiftLoading || isCashLoading)
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );

  if (giftError || cashError)
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Sorry, An error occured. Kindly, Refresh</p>
      </div>
    );

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] pt-32 ">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20 ">
        <PageTitle
          title="Gift Registry"
          description="View gift registry details"
        />
        {/* <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer">
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div> */}
        <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <div className="max-w-md ">
            <h1 className="text-[28px] font-semibold mb-3">
              {selectedEvent?.title || 'Gift Registry'}
            </h1>
            <p className="text-sm text-grey-950 mb-7">
              {selectedEvent?.description || 'Event gift registry'}
            </p>

            <div className=" flex gap-4 mb-5">
              <div className="flex items-center gap-1 bg-primary-250 pl-2.5 pr-2 py-0.5 rounded-2xl">
                <Gift variant="Bulk" size={12} color="#4D55F2" />
                <span className="text-xs italic font-medium text-primary">
                  {registry?.items?.length || 0} Gift Items
                </span>
              </div>
              <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 rounded-2xl py-0.5">
                <Moneys variant="Bulk" size={12} color="#FF885E" />
                <span className="text-cus-orange-250 text-xs italic font-medium">
                  {registry?.cashItems?.length || 0} Cash gifts{' '}
                </span>
              </div>
            </div>
          </div>

          <img
            src={gift}
            alt="invite-card"
            className="hidden md:block rounded-br-2xl"
          />
        </div>

        <div className="bg-white rounded-2xl mb-2.5 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-x divide-grey-850">
            {guestStats.map((stat, index) => (
              <div key={index} className=" pr-3 pl-4 pt-3 pb-5">
                <div className="flex justify-end mb-3">{stat.icon}</div>
                <div className="text-[32px] italic font-bold mb-1.5">
                  {stat.count}
                </div>
                <div className="text-grey-250 text-xs font-medium uppercase tracking-[0.10em]">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white flex justify-between py-6 px-4 rounded-xl shadow-[0px_12px_120px_0px_#5F5F5F0F] mb-8">
          <div>
            <p className="text-3xl font-bold italic tracking-[0.04em]">
              {registry?.amountReceived?.toLocaleString()}
            </p>
            <p className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              Amount Received
            </p>
          </div>
          <button
            className="md:px-4 px-2 py-1 md:py-2 bg-primary rounded-full text-[10px] h-[40px] md:h-auto sm:text-sm font-medium text-white min-w-max w-fit flex items-center gap-2 cursor-pointer "
            onClick={() => navigate('/withdrawal/select-account')}>
            Withdraw Balance{' '}
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.4"
                d="M9.9974 18.3337C14.5998 18.3337 18.3307 14.6027 18.3307 10.0003C18.3307 5.39795 14.5998 1.66699 9.9974 1.66699C5.39502 1.66699 1.66406 5.39795 1.66406 10.0003C1.66406 14.6027 5.39502 18.3337 9.9974 18.3337Z"
                fill="white"
              />
              <path
                d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                fill="white"
              />
            </svg>
          </button>
        </div>

        <div className="mb-6 flex justify-between items-center">
          <div className="rounded-full p-1 inline-flex">
            <button
              className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                activeTab === 'gifts'
                  ? 'bg-primary text-white'
                  : 'text-gray-700'
              }`}
              onClick={() => setActiveTab('gifts')}>
              Gift Items • {registry?.items?.length}
            </button>
            <button
              className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                activeTab === 'cash' ? 'bg-primary text-white' : 'text-gray-700'
              }`}
              onClick={() => setActiveTab('cash')}>
              Cash Gifts • {registry?.cashItems?.length || 0}
            </button>
          </div>
          <button
            type="button"
            onClick={() => setIsGiftRegistryModalOpen(true)}
            className="flex items-center gap-1 text-primary font-medium text-xs rounded-full bg-primary-150 px-2 py-1.5">
            <AddCircle size="24" color="#4D55F2" variant="Bulk" />
            <span> Add New</span>
          </button>
        </div>

        {activeTab === 'gifts' &&
          (registry.items.length > 0 ? (
            <div
              className="space-y-4 max-h-[900px] overflow-y-auto pr-2 scrollbar-thin [&::-webkit-scrollbar]:hidden scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              onScroll={handleTabScroll}>
              {registry.items.map((item: any) => (
                <div
                  key={item.id}
                  className="bg-white relative rounded-xl flex flex-col md:flex-row items-center gap-4.5 cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() =>
                    navigate(`/gift-registry/${id}/gift/${item.id}`)
                  }>
                  <div
                    className="absolute top-4 right-4 z-10"
                    ref={dropdownRef}>
                    <button
                      onClick={handleDropdownToggle}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors">
                      <MoreVertical size={20} color="#999999" />
                    </button>

                    {/* Dropdown Menu */}
                    {isDropdownOpen && (
                      <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[160px] z-20">
                        <button
                          onClick={handleHideFromList}
                          className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2">
                          <EyeSlash size={16} color="#5F66F3" variant="Bulk" />
                          Hide from List
                        </button>
                        <button
                          onClick={handleEditGiftItem}
                          className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2">
                          <Edit2 size={16} color="#5F66F3" variant="Bulk" />
                          Edit Gift Item
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="w-full rounded-t-xl md:rounded-[unset] md:w-[155px] h-[184px] bg-gray-200 md:rounded-l-lg overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 text-center md:text-left p-4">
                    <h3 className="text-[22px] font-medium text-grey-750">
                      {item.name}
                    </h3>
                    <p className="text-base text-grey-100">
                      {item.description}{' '}
                    </p>
                    <div className="mt-5.5 mb-4 flex flex-col md:flex-row gap-2 items-center ">
                      <div className="flex items-center gap-2  bg-light-blue-150 text-orange-700 px-2.5 py-1.5 rounded-full">
                        <Tag2 size={12} variant="Bulk" color="#5925DC" />
                        <span className="text-primary text-sm font-semibold">
                          ₦{item.price.toLocaleString()}
                        </span>
                      </div>
                      {item.reserved > 0 && (
                        <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-orange-700 text-sm px-2.5 py-1.5 rounded-full">
                          <Box size={12} variant="Bulk" color="#C4320A" />
                          <span> Reserved • {item.reserved}</span>
                        </div>
                      )}
                      {item.purchased > 0 && (
                        <span className="flex items-center gap-1 font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                          <BuyCrypto size={12} variant="Bulk" color="#3CC35C" />{' '}
                          Purchased • {item.purchased}
                        </span>
                      )}
                    </div>
                    {/* <div className="flex items-center justify-center md:justify-start mb-7 md:mb-0 gap-2">
                      <p className="text-sm text-grey-100">
                        Hide Item from List
                      </p>
                      <EyeSlash variant="Bulk" size={20} color="#999999" />
                    </div> */}
                  </div>
                </div>
              ))}

              {/* Loading indicator for infinite scroll */}
              {isFetchingNextGiftPage && (
                <div className="flex justify-center py-4">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                </div>
              )}

              {/* End of list indicator
              {!hasNextGiftPage && registry.items.length > 0 && (
                <div className="text-center py-4 text-gray-500 text-sm">
                  No more gift items to load
                </div>
              )} */}
            </div>
          ) : (
            <div className="bg-white p-14 rounded-2xl flex flex-col items-center justify-center">
              <img src={empty} alt="empty-state" />
              <h3 className="text-[28px] -tracking-[0.04em] font-medium mt-3 mb-2">
                No Gift Item here currently{' '}
              </h3>
              <p className="text-grey-250 text-base mb-5 text-center">
                You have no Gifts Items currently, <br /> Get started by
                clicking the button below
              </p>
              <button
                type="button"
                onClick={() => setIsGiftRegistryModalOpen(true)}
                className="bg-primary text-white font-medium py-2 px-4 rounded-full mb-25">
                Add Gift Items
              </button>
            </div>
          ))}

        {activeTab === 'cash' &&
          (registry.cashItems && registry.cashItems.length > 0 ? (
            <div
              className="max-h-[600px] [&::-webkit-scrollbar]:hidden overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              onScroll={handleTabScroll}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {registry.cashItems.map((item: any) => (
                  <div
                    key={item.id}
                    className="bg-white rounded-2xl px-4 py-5 cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() =>
                      navigate(`/gift-registry/${id}/cash/${item.id}`)
                    }>
                    <Moneys variant="Bulk" size={56} color="#E2CBC1" />
                    <p className="text-[32px] font-bold ">
                      ₦{item.amount.toLocaleString()}
                    </p>
                    <p className="text-grey-250 text-base italic mb-13.5">
                      {item.description}
                    </p>
                    {item.received > 0 && (
                      <div className="flex items-center gap-1 w-fit font-bold italic bg-grin text-grin-100 text-sm px-2.5 py-1.5 rounded-full">
                        <Box size={12} variant="Bulk" color="#3CC35C" />
                        <span> Reserved • {item.received}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Loading indicator for infinite scroll */}
              {isFetchingNextCashPage && (
                <div className="flex justify-center py-4">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                </div>
              )}

              {/* End of list indicator
              {!hasNextCashPage && registry.cashItems.length > 0 && (
                <div className="text-center py-4 text-gray-500 text-sm">
                  No more cash gifts to load
                </div>
              )} */}
            </div>
          ) : (
            <div className="bg-white p-14 rounded-2xl flex flex-col items-center justify-center">
              <img src={empty} alt="empty-state" />
              <h3 className="text-[28px] -tracking-[0.04em] font-medium mt-3 mb-2">
                No Cash gifts here currently
              </h3>
              <p className="text-grey-250 text-base mb-5 text-center">
                You have no Cash gifts currently.
                <br /> Get started by clicking the button below
              </p>
              <button
                type="button"
                onClick={() => setIsGiftRegistryModalOpen(true)}
                className="bg-primary text-white font-medium py-2 px-4 rounded-full mb-25">
                Add Cash gift
              </button>
            </div>
          ))}
      </div>
      {isGiftRegistryModalOpen && (
        <CreateGiftRegistry onClose={() => setIsGiftRegistryModalOpen(false)} />
      )}
    </div>
  );
};
